import requests
import time

print("Checking documents...")
time.sleep(2)

login_data = {'username': 'gab<PERSON>', 'password': 'gabriel123'}
response = requests.post('http://localhost:5000/api/auth/login', json=login_data, timeout=10)

if response.status_code == 200:
    token = response.json().get('token')
    headers = {'Authorization': f'Bearer {token}'}
    
    try:
        response = requests.get('http://localhost:5000/api/documents/list', headers=headers, timeout=30)
        if response.status_code == 200:
            documents = response.json().get('documents', [])
            print(f'Found {len(documents)} documents:')
            for doc in documents:
                print(f'  - {doc.get("filename", "Unknown")}')
        else:
            print('Error:', response.status_code, response.text)
    except Exception as e:
        print('Request error:', e)
else:
    print('Login failed')
