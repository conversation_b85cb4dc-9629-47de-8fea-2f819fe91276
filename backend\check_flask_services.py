"""
<PERSON><PERSON>t to check the status of services in the running Flask app.
"""
import requests
import json

def check_flask_services():
    """Check the status of services in the Flask app."""
    print("Checking Flask app services...")
    
    # Login first
    login_url = "http://localhost:5000/api/auth/login"
    login_data = {"username": "gabriel", "password": "gabriel123"}
    
    try:
        response = requests.post(login_url, json=login_data, timeout=10)
        if response.status_code != 200:
            print(f"❌ Login failed: {response.text}")
            return
        
        token = response.json().get("token")
        headers = {"Authorization": f"Bearer {token}"}
        print("✅ Login successful")
        
        # Test 1: Health check
        print("\n1. Testing health check...")
        health_url = "http://localhost:5000/api/health"
        response = requests.get(health_url, timeout=10)
        print(f"Health check: {response.status_code}")
        if response.status_code == 200:
            print(f"Health: {response.json()}")
        
        # Test 2: Document list (this uses VectorDBService)
        print("\n2. Testing document list...")
        list_url = "http://localhost:5000/api/documents/list"
        response = requests.get(list_url, headers=headers, timeout=10)
        print(f"Document list: {response.status_code}")
        if response.status_code == 200:
            documents = response.json().get("documents", [])
            print(f"Documents found: {len(documents)}")
        else:
            print(f"Error: {response.text}")
        
        # Test 3: Document search (this uses VectorDBService and EmbeddingService)
        print("\n3. Testing document search...")
        search_url = "http://localhost:5000/api/documents/search"
        search_data = {"query": "test", "filters": {}}
        response = requests.post(search_url, json=search_data, headers=headers, timeout=30)
        print(f"Document search: {response.status_code}")
        if response.status_code == 200:
            results = response.json().get("results", [])
            print(f"Search results: {len(results)}")
        else:
            print(f"Search error: {response.text}")
        
        # Test 4: Try to upload a very simple document
        print("\n4. Testing simple document upload...")
        
        # Create a minimal test file
        import tempfile
        import os
        
        test_content = "Simple test document for debugging."
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(test_content)
            temp_file_path = f.name
        
        try:
            upload_url = "http://localhost:5000/api/documents/upload"
            
            with open(temp_file_path, 'rb') as f:
                files = {'file': ('debug_test.txt', f, 'text/plain')}
                response = requests.post(upload_url, files=files, headers=headers, timeout=60)
            
            print(f"Upload response: {response.status_code}")
            if response.status_code == 200:
                result = response.json()
                print(f"Upload result: {result}")
                
                # Immediately check if it appears in the list
                print("\n5. Checking if uploaded document appears...")
                response = requests.get(list_url, headers=headers, timeout=10)
                if response.status_code == 200:
                    documents = response.json().get("documents", [])
                    print(f"Documents after upload: {len(documents)}")
                    for doc in documents:
                        print(f"  - {doc}")
                else:
                    print(f"List error after upload: {response.text}")
            else:
                print(f"Upload error: {response.text}")
                
        finally:
            os.unlink(temp_file_path)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main function."""
    print("Flask services diagnostic\n")
    check_flask_services()
    print("\nDiagnostic completed.")

if __name__ == "__main__":
    main()
