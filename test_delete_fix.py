import requests
import tempfile
import os
import json

def test_delete_fix():
    """Test the delete fix."""
    print("🧪 Testing delete functionality fix...")
    
    # Login
    login_data = {'username': 'gabriel', 'password': 'gabriel123'}
    response = requests.post('http://localhost:5000/api/auth/login', json=login_data, timeout=10)
    
    if response.status_code != 200:
        print('❌ Login failed')
        return False
    
    token = response.json().get('token')
    headers = {'Authorization': f'Bearer {token}'}
    print('✅ Login successful')
    
    # Upload a test document
    print('\n📄 Uploading test document...')
    test_content = "This is a test document for delete functionality testing."
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        f.write(test_content)
        temp_file_path = f.name
    
    try:
        with open(temp_file_path, 'rb') as f:
            files = {'file': ('delete_test.txt', f, 'text/plain')}
            response = requests.post('http://localhost:5000/api/documents/upload', 
                                   files=files, headers=headers, timeout=60)
        
        if response.status_code != 200:
            print(f'❌ Upload failed: {response.status_code}')
            return False
        
        print('✅ Test document uploaded successfully')
        
        # Verify document exists
        print('\n📋 Verifying document exists...')
        response = requests.get('http://localhost:5000/api/documents/list', headers=headers, timeout=10)
        
        if response.status_code != 200:
            print(f'❌ Failed to list documents: {response.status_code}')
            return False
        
        documents = response.json().get('documents', [])
        test_doc_exists = any(doc.get('filename') == 'delete_test.txt' for doc in documents)
        
        if not test_doc_exists:
            print('❌ Test document not found in list')
            return False
        
        print('✅ Test document found in list')
        
        # Test delete
        print('\n🗑️  Testing delete...')
        delete_response = requests.delete('http://localhost:5000/api/documents/delete/delete_test.txt', 
                                        headers=headers, timeout=10)
        
        print(f'Delete status: {delete_response.status_code}')
        
        try:
            delete_data = delete_response.json()
            print(f'Delete response: {json.dumps(delete_data, indent=2)}')
        except:
            print(f'Delete response text: {delete_response.text}')
        
        # Check if delete was successful
        if delete_response.status_code == 200:
            print('✅ Delete returned 200 - SUCCESS!')
            
            # Verify document is gone from list
            print('\n📋 Verifying document is removed from list...')
            response = requests.get('http://localhost:5000/api/documents/list', headers=headers, timeout=10)
            
            if response.status_code == 200:
                new_documents = response.json().get('documents', [])
                test_doc_still_exists = any(doc.get('filename') == 'delete_test.txt' for doc in new_documents)
                
                if test_doc_still_exists:
                    print('❌ Document still in list after delete')
                    return False
                else:
                    print('✅ Document removed from list')
            
            # Verify file is gone from disk
            print('\n💾 Verifying file is removed from disk...')
            download_response = requests.get('http://localhost:5000/api/documents/download/delete_test.txt', 
                                           headers=headers, timeout=10)
            
            if download_response.status_code == 404:
                print('✅ File removed from disk (download returns 404)')
                return True
            else:
                print(f'❌ File still accessible (download returns {download_response.status_code})')
                return False
        else:
            print(f'❌ Delete failed with status {delete_response.status_code}')
            return False
            
    finally:
        os.unlink(temp_file_path)

def main():
    """Main test function."""
    print("🧪 Delete Functionality Fix Test\n")
    
    success = test_delete_fix()
    
    if success:
        print("\n🎉 Delete functionality fix SUCCESSFUL!")
        print("✅ Delete endpoint now returns 200")
        print("✅ Documents are properly removed")
        print("✅ Files are deleted from disk")
        print("\n🌐 Frontend delete buttons should now work correctly!")
    else:
        print("\n💥 Delete functionality still has issues!")

if __name__ == "__main__":
    main()
