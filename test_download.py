import requests
import os

def test_download():
    print("Testing document download functionality...")
    
    # Login
    login_data = {'username': 'gabriel', 'password': 'gabriel123'}
    response = requests.post('http://localhost:5000/api/auth/login', json=login_data, timeout=5)
    
    if response.status_code != 200:
        print('Login failed')
        return
    
    token = response.json().get('token')
    headers = {'Authorization': f'Bearer {token}'}
    
    # Get document list
    response = requests.get('http://localhost:5000/api/documents/list', headers=headers, timeout=10)
    
    if response.status_code != 200:
        print('Failed to get document list')
        return
    
    documents = response.json().get('documents', [])
    print(f'Found {len(documents)} documents:')
    for doc in documents:
        print(f'  - {doc.get("filename", "Unknown")}')
    
    # Test download for each document
    for doc in documents:
        filename = doc.get('filename')
        if filename:
            print(f'\nTesting download for: {filename}')
            
            # Test download API
            download_url = f'http://localhost:5000/api/documents/download/{filename}'
            print(f'Download URL: {download_url}')
            
            try:
                response = requests.get(download_url, headers=headers, timeout=10)
                print(f'Download response status: {response.status_code}')
                
                if response.status_code == 200:
                    print(f'Content-Type: {response.headers.get("Content-Type", "Unknown")}')
                    print(f'Content-Length: {len(response.content)} bytes')
                    print(f'Content preview: {response.content[:100]}...')
                    
                    # Check if file exists on disk
                    file_path = os.path.join('backend', 'uploads', 'gabriel', filename)
                    if os.path.exists(file_path):
                        file_size = os.path.getsize(file_path)
                        print(f'File exists on disk: {file_size} bytes')
                    else:
                        print('File does NOT exist on disk!')
                        
                else:
                    print(f'Download failed: {response.text}')
                    
            except Exception as e:
                print(f'Download error: {e}')

if __name__ == "__main__":
    test_download()
