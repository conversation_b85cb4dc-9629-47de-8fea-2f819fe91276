"""
Test script to check Qdrant directly without the Flask app running.
"""
import sys

def test_qdrant_direct():
    """Test Qdrant directly."""
    print("Testing Qdrant directly...")
    
    try:
        from src.services.vector_db_service import VectorDBService
        
        # Create vector DB service
        vector_service = VectorDBService()
        
        if not vector_service.client:
            print("❌ Qdrant client not initialized")
            return False
        
        print("✅ Qdrant client initialized")
        
        # Get collection info
        collection_info = vector_service.client.get_collection(vector_service.collection_name)
        print(f"Collection info: {collection_info}")
        
        # Try to scroll through all points
        print("\nScrolling through all points...")
        scroll_result = vector_service.client.scroll(
            collection_name=vector_service.collection_name,
            limit=10,
            with_payload=True,
            with_vectors=False
        )
        
        points = scroll_result[0]
        print(f"Found {len(points)} points total")
        
        gabriel_count = 0
        for i, point in enumerate(points):
            print(f"\nPoint {i+1}:")
            print(f"  ID: {point.id}")
            
            if "metadata" in point.payload:
                metadata = point.payload["metadata"]
                print(f"  Source: {metadata.get('source', 'N/A')}")
                print(f"  User ID: {metadata.get('user_id', 'N/A')}")
                print(f"  User Role: {metadata.get('user_role', 'N/A')}")
                
                if metadata.get('user_id') == 'gabriel':
                    gabriel_count += 1
            else:
                print("  No metadata found")
        
        print(f"\nTotal points for gabriel: {gabriel_count}")
        
        # Try the problematic scroll with filter
        print("\nTesting scroll with filter...")
        try:
            from qdrant_client.http import models
            
            filter_query = models.Filter(
                must=[
                    models.FieldCondition(
                        key="metadata.user_id",
                        match=models.MatchValue(value="gabriel")
                    )
                ]
            )
            
            gabriel_scroll = vector_service.client.scroll(
                collection_name=vector_service.collection_name,
                limit=10,
                with_payload=True,
                with_vectors=False,
                scroll_filter=filter_query  # Try scroll_filter instead of filter
            )
            
            gabriel_points = gabriel_scroll[0]
            print(f"✅ Filter scroll successful: found {len(gabriel_points)} points for gabriel")
            
            for point in gabriel_points:
                metadata = point.payload.get("metadata", {})
                print(f"  - {metadata.get('source', 'Unknown')}")
            
        except Exception as filter_error:
            print(f"❌ Filter scroll failed: {filter_error}")
            
            # Try without filter
            print("Trying scroll without filter...")
            all_scroll = vector_service.client.scroll(
                collection_name=vector_service.collection_name,
                limit=10,
                with_payload=True,
                with_vectors=False
            )
            
            all_points = all_scroll[0]
            gabriel_docs = []
            
            for point in all_points:
                metadata = point.payload.get("metadata", {})
                if metadata.get("user_id") == "gabriel":
                    gabriel_docs.append(metadata.get("source", "Unknown"))
            
            print(f"✅ Manual filter: found {len(gabriel_docs)} documents for gabriel")
            for doc in gabriel_docs:
                print(f"  - {doc}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing Qdrant: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    print("Direct Qdrant test\n")
    
    success = test_qdrant_direct()
    
    if success:
        print("\n🎉 Qdrant test completed!")
    else:
        print("\n💥 Qdrant test failed!")

if __name__ == "__main__":
    main()
