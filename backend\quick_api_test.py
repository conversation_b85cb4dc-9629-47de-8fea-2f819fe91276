"""
Quick API test with timeout.
"""
import requests

def quick_test():
    """Quick test with timeout."""
    print("Quick API test...")

    try:
        # Login
        print("1. Login...")
        login_url = "http://localhost:5000/api/auth/login"
        login_data = {"username": "gab<PERSON>", "password": "gabriel123"}

        response = requests.post(login_url, json=login_data, timeout=5)
        if response.status_code != 200:
            print(f"❌ Login failed: {response.text}")
            return

        token = response.json().get("token")
        headers = {"Authorization": f"Bearer {token}"}
        print("✅ Login successful")

        # Test document list with timeout
        print("2. Testing document list...")
        list_url = "http://localhost:5000/api/documents/list"

        try:
            response = requests.get(list_url, headers=headers, timeout=10)
            print(f"List response: {response.status_code}")

            if response.status_code == 200:
                documents = response.json().get("documents", [])
                print(f"✅ Found {len(documents)} documents:")
                for doc in documents:
                    print(f"  - {doc}")
            else:
                print(f"❌ List failed: {response.text}")
        except requests.exceptions.Timeout:
            print("❌ Document list request timed out")
        except Exception as e:
            print(f"❌ Document list error: {e}")

    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    quick_test()
