import requests
import json

def debug_download():
    print("Debugging download functionality...")
    
    # Login
    login_data = {'username': 'gab<PERSON>', 'password': 'gabriel123'}
    response = requests.post('http://localhost:5000/api/auth/login', json=login_data, timeout=5)
    
    if response.status_code != 200:
        print('Login failed')
        return
    
    token = response.json().get('token')
    headers = {'Authorization': f'Bearer {token}'}
    
    # Test download with detailed error info
    filename = 'test_diabetes.txt'
    download_url = f'http://localhost:5000/api/documents/download/{filename}'
    
    print(f'Testing download: {download_url}')
    
    try:
        response = requests.get(download_url, headers=headers, timeout=10)
        print(f'Status: {response.status_code}')
        print(f'Headers: {dict(response.headers)}')
        
        if response.status_code == 200:
            print(f'Success! Content length: {len(response.content)}')
            print(f'Content type: {response.headers.get("Content-Type")}')
            print(f'Content disposition: {response.headers.get("Content-Disposition")}')
        else:
            print(f'Error response: {response.text}')
            
            # Try to parse as JSON
            try:
                error_data = response.json()
                print(f'Error details: {json.dumps(error_data, indent=2)}')
            except:
                print('Could not parse error as JSON')
                
    except Exception as e:
        print(f'Request failed: {e}')

if __name__ == "__main__":
    debug_download()
