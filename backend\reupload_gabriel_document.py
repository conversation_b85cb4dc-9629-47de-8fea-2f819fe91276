"""
<PERSON><PERSON><PERSON> to re-upload <PERSON>'s document through the API to ensure it gets processed.
"""
import os
import requests

def reupload_gabriel_document():
    """Re-upload <PERSON>'s document through the API."""
    print("Re-uploading <PERSON>'s document through API...")
    
    # Document details
    filename = "Backend_Four_Hour_Test_1.pdf"
    file_path = os.path.join("uploads", "gabriel", filename)
    
    # Check if file exists
    if not os.path.exists(file_path):
        print(f"Error: File {file_path} does not exist")
        return False
    
    print(f"File found: {file_path}")
    file_size = os.path.getsize(file_path)
    print(f"File size: {file_size} bytes")
    
    try:
        # Step 1: Login to get token
        print("Logging in as gabriel...")
        login_url = "http://localhost:5000/api/auth/login"
        login_data = {
            "username": "gabriel",
            "password": "gabriel123"
        }
        
        response = requests.post(login_url, json=login_data)
        if response.status_code != 200:
            print(f"Login failed: {response.status_code} - {response.text}")
            return False
        
        token = response.json().get("token")
        print("Successfully logged in")
        
        # Step 2: Upload the document
        print("Uploading document...")
        upload_url = "http://localhost:5000/api/documents/upload"
        headers = {"Authorization": f"Bearer {token}"}
        
        with open(file_path, 'rb') as f:
            files = {'file': (filename, f, 'application/pdf')}
            response = requests.post(upload_url, files=files, headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            print(f"Upload successful: {result}")
            
            # Step 3: Verify the document was processed
            print("Verifying document was processed...")
            list_url = "http://localhost:5000/api/documents/list"
            response = requests.get(list_url, headers=headers)
            
            if response.status_code == 200:
                documents = response.json().get("documents", [])
                print(f"Found {len(documents)} documents for gabriel:")
                for doc in documents:
                    print(f"  - {doc}")
                return len(documents) > 0
            else:
                print(f"Error listing documents: {response.status_code} - {response.text}")
                return False
        else:
            print(f"Upload failed: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"Error re-uploading document: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    print("Re-upload Gabriel's document script\n")
    
    success = reupload_gabriel_document()
    
    if success:
        print("\n✅ Document re-upload and processing completed successfully!")
    else:
        print("\n❌ Document re-upload failed!")

if __name__ == "__main__":
    main()
