"""
Test script to check if <PERSON>'s document was processed and stored in Qdrant.
"""
import os
import sys
import requests
import json

def test_api_document_list():
    """Test the API endpoint for listing documents."""
    print("Testing API document list endpoint...")

    # First, we need to login to get a token
    login_url = "http://localhost:5000/api/auth/login"
    login_data = {
        "username": "gabriel",
        "password": "gabriel123"  # Correct password from auth_service.py
    }

    try:
        # Login
        response = requests.post(login_url, json=login_data)
        if response.status_code == 200:
            token = response.json().get("token")
            print(f"Successfully logged in as gabriel")

            # List documents
            list_url = "http://localhost:5000/api/documents/list"
            headers = {"Authorization": f"Bearer {token}"}

            response = requests.get(list_url, headers=headers)
            if response.status_code == 200:
                documents = response.json().get("documents", [])
                print(f"Found {len(documents)} documents for gabriel:")
                for doc in documents:
                    print(f"  - {doc}")
            else:
                print(f"Error listing documents: {response.status_code} - {response.text}")
        else:
            print(f"Login failed: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"Error testing API: {e}")

def test_file_system():
    """Test if the file exists in the file system."""
    print("\nTesting file system...")

    gabriel_dir = "uploads/gabriel"
    if os.path.exists(gabriel_dir):
        files = os.listdir(gabriel_dir)
        print(f"Files in {gabriel_dir}: {files}")

        pdf_file = "Backend_Four_Hour_Test_1.pdf"
        pdf_path = os.path.join(gabriel_dir, pdf_file)
        if os.path.exists(pdf_path):
            file_size = os.path.getsize(pdf_path)
            print(f"File {pdf_file} exists, size: {file_size} bytes")
        else:
            print(f"File {pdf_file} not found")
    else:
        print(f"Directory {gabriel_dir} does not exist")

def test_qdrant_direct():
    """Test Qdrant database directly using HTTP API."""
    print("\nTesting Qdrant database directly...")

    try:
        # Check if Qdrant is running
        qdrant_url = "http://localhost:6333"
        response = requests.get(f"{qdrant_url}/collections")
        if response.status_code == 200:
            collections = response.json()
            print(f"Qdrant collections: {collections}")

            # Check health_documents collection
            if "health_documents" in [c["name"] for c in collections.get("result", {}).get("collections", [])]:
                # Get collection info
                collection_url = f"{qdrant_url}/collections/health_documents"
                response = requests.get(collection_url)
                if response.status_code == 200:
                    collection_info = response.json()
                    print(f"Collection info: {collection_info}")

                    # Search for documents with user_id = gabriel
                    search_url = f"{qdrant_url}/collections/health_documents/points/scroll"
                    search_data = {
                        "filter": {
                            "must": [
                                {
                                    "key": "metadata.user_id",
                                    "match": {"value": "gabriel"}
                                }
                            ]
                        },
                        "limit": 10,
                        "with_payload": True,
                        "with_vector": False
                    }

                    response = requests.post(search_url, json=search_data)
                    if response.status_code == 200:
                        results = response.json()
                        points = results.get("result", {}).get("points", [])
                        print(f"Found {len(points)} points for gabriel in Qdrant")

                        # Group by source
                        sources = {}
                        for point in points:
                            payload = point.get("payload", {})
                            metadata = payload.get("metadata", {})
                            source = metadata.get("source", "unknown")
                            if source not in sources:
                                sources[source] = 0
                            sources[source] += 1

                        print("Documents by source:")
                        for source, count in sources.items():
                            print(f"  - {source}: {count} chunks")
                    else:
                        print(f"Error searching Qdrant: {response.status_code} - {response.text}")
            else:
                print("health_documents collection not found")
        else:
            print(f"Qdrant not accessible: {response.status_code}")
    except Exception as e:
        print(f"Error testing Qdrant: {e}")

def main():
    """Run all tests."""
    print("Testing Gabriel's document processing...\n")

    test_file_system()
    test_api_document_list()
    test_qdrant_direct()

    print("\nTests completed.")

if __name__ == "__main__":
    main()
