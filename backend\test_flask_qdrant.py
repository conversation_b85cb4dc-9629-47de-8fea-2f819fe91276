"""
Test script to check if the Flask app's Qdrant client is working via API.
"""
import requests
import json

def test_qdrant_via_api():
    """Test Qdrant functionality via the Flask API."""
    print("Testing Qdrant functionality via Flask API...")
    
    # Step 1: Login
    print("1. Logging in...")
    login_url = "http://localhost:5000/api/auth/login"
    login_data = {"username": "gab<PERSON>", "password": "gabriel123"}
    
    try:
        response = requests.post(login_url, json=login_data, timeout=10)
        if response.status_code != 200:
            print(f"❌ Login failed: {response.text}")
            return False
        
        token = response.json().get("token")
        headers = {"Authorization": f"Bearer {token}"}
        print("✅ Login successful")
        
        # Step 2: Test search functionality (this uses Qdrant)
        print("\n2. Testing search functionality...")
        search_url = "http://localhost:5000/api/documents/search"
        search_data = {
            "query": "diabetes",
            "filters": {}
        }
        
        response = requests.post(search_url, json=search_data, headers=headers, timeout=30)
        print(f"Search response: {response.status_code}")
        
        if response.status_code == 200:
            results = response.json().get("results", [])
            print(f"✅ Search successful, found {len(results)} results")
            for i, result in enumerate(results):
                print(f"  Result {i+1}: {result.get('metadata', {}).get('source', 'Unknown')}")
        else:
            print(f"❌ Search failed: {response.text}")
        
        # Step 3: Test chat functionality (this also uses Qdrant for context)
        print("\n3. Testing chat functionality...")
        chat_url = "http://localhost:5000/api/chat"
        chat_data = {"question": "What is diabetes?"}
        
        response = requests.post(chat_url, json=chat_data, headers=headers, timeout=30)
        print(f"Chat response: {response.status_code}")
        
        if response.status_code == 200:
            answer = response.json().get("answer", "")
            print(f"✅ Chat successful, answer length: {len(answer)} characters")
            print(f"Answer preview: {answer[:100]}...")
        else:
            print(f"❌ Chat failed: {response.text}")
        
        # Step 4: Upload a small test document
        print("\n4. Testing document upload with a small test file...")
        
        # Create a small test PDF content (just text for now)
        test_content = "This is a test document about diabetes. Diabetes is a chronic condition."
        
        # Create a temporary text file instead of PDF for simplicity
        import tempfile
        import os
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(test_content)
            temp_file_path = f.name
        
        try:
            upload_url = "http://localhost:5000/api/documents/upload"
            
            with open(temp_file_path, 'rb') as f:
                files = {'file': ('test_diabetes.txt', f, 'text/plain')}
                response = requests.post(upload_url, files=files, headers=headers, timeout=60)
            
            print(f"Upload response: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Upload successful: {result}")
                
                # Wait a moment and check if document appears
                import time
                time.sleep(3)
                
                list_url = "http://localhost:5000/api/documents/list"
                response = requests.get(list_url, headers=headers, timeout=10)
                
                if response.status_code == 200:
                    documents = response.json().get("documents", [])
                    print(f"Documents after test upload: {len(documents)}")
                    for doc in documents:
                        print(f"  - {doc}")
                    
                    if len(documents) > 0:
                        print("✅ Test document successfully processed and listed!")
                        return True
                    else:
                        print("❌ Test document uploaded but not listed")
                        return False
                else:
                    print(f"❌ Failed to list documents after upload: {response.text}")
                    return False
            else:
                print(f"❌ Upload failed: {response.text}")
                return False
                
        finally:
            # Clean up temp file
            os.unlink(temp_file_path)
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    print("Testing Flask app's Qdrant functionality\n")
    
    success = test_qdrant_via_api()
    
    if success:
        print("\n🎉 Flask app's Qdrant functionality is working!")
    else:
        print("\n💥 Flask app's Qdrant functionality has issues!")

if __name__ == "__main__":
    main()
