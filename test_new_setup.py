"""
Test script for the new Qdrant setup.
"""
import requests
import time
import tempfile
import os

def test_services():
    """Test all services in the new setup."""
    print("Testing new AI Health Chatbot setup...\n")

    # Test 1: Qdrant service
    print("1. Testing Qdrant service...")
    try:
        response = requests.get("http://localhost:6333/", timeout=5)
        if response.status_code == 200:
            print("✅ Qdrant service is healthy")

            # Get collections
            collections_response = requests.get("http://localhost:6333/collections", timeout=5)
            if collections_response.status_code == 200:
                collections = collections_response.json()
                print(f"   Collections: {[c['name'] for c in collections.get('result', {}).get('collections', [])]}")
            else:
                print("   No collections found yet (this is normal for a new setup)")
        else:
            print(f"❌ Qdrant service unhealthy: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Qdrant service error: {e}")
        return False

    # Test 2: Backend service
    print("\n2. Testing backend service...")
    try:
        response = requests.get("http://localhost:5000/api/health", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ Backend service is healthy: {health_data.get('status')}")
        else:
            print(f"❌ Backend service unhealthy: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Backend service error: {e}")
        return False

    # Test 3: Authentication
    print("\n3. Testing authentication...")
    try:
        login_data = {"username": "gabriel", "password": "gabriel123"}
        response = requests.post("http://localhost:5000/api/auth/login", json=login_data, timeout=10)
        if response.status_code == 200:
            token = response.json().get("token")
            print("✅ Authentication working")

            # Test 4: Document list (should work now with new Qdrant)
            print("\n4. Testing document list...")
            headers = {"Authorization": f"Bearer {token}"}
            response = requests.get("http://localhost:5000/api/documents/list", headers=headers, timeout=10)
            if response.status_code == 200:
                documents = response.json().get("documents", [])
                print(f"✅ Document list working: {len(documents)} documents found")
                for doc in documents:
                    print(f"   - {doc.get('filename', 'Unknown')}")
            else:
                print(f"❌ Document list failed: {response.status_code} - {response.text}")
                return False

            # Test 5: Document upload
            print("\n5. Testing document upload...")
            test_content = "This is a test document for the new Qdrant setup. It contains information about diabetes management."

            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
                f.write(test_content)
                temp_file_path = f.name

            try:
                with open(temp_file_path, 'rb') as f:
                    files = {'file': ('test_new_setup.txt', f, 'text/plain')}
                    response = requests.post("http://localhost:5000/api/documents/upload",
                                           files=files, headers=headers, timeout=60)

                if response.status_code == 200:
                    result = response.json()
                    print(f"✅ Document upload successful: {result.get('message')}")

                    # Wait and check if document appears in list
                    print("   Waiting for document processing...")
                    time.sleep(3)

                    response = requests.get("http://localhost:5000/api/documents/list", headers=headers, timeout=10)
                    if response.status_code == 200:
                        documents = response.json().get("documents", [])
                        new_doc_found = any(doc.get('filename') == 'test_new_setup.txt' for doc in documents)
                        if new_doc_found:
                            print("✅ Document appears in list - full pipeline working!")
                        else:
                            print("⚠️  Document uploaded but not in list yet")
                    else:
                        print("⚠️  Could not verify document in list")
                else:
                    print(f"❌ Document upload failed: {response.status_code} - {response.text}")
                    return False
            finally:
                os.unlink(temp_file_path)

        else:
            print(f"❌ Authentication failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Authentication error: {e}")
        return False

    return True

def main():
    """Main test function."""
    print("AI Health Chatbot New Setup Test\n")

    if test_services():
        print("\n🎉 All tests passed! The new setup is working correctly.")
        print("\nYou can now:")
        print("1. Open the frontend at http://localhost:3000")
        print("2. Login as gabriel/gabriel123 or drmurilo/drmurilo123")
        print("3. Upload documents in the Document Management tab")
        print("4. Chat with the AI using your uploaded documents")
    else:
        print("\n💥 Some tests failed. Check the services and try again.")

if __name__ == "__main__":
    main()
