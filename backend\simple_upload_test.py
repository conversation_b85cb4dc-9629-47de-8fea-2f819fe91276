"""
Simple script to test document upload via API.
"""
import os
import requests
import time

def test_upload():
    """Test uploading <PERSON>'s document."""
    print("Testing document upload via API...")
    
    # Step 1: Login
    print("1. Logging in...")
    login_url = "http://localhost:5000/api/auth/login"
    login_data = {"username": "gabriel", "password": "gabriel123"}
    
    try:
        response = requests.post(login_url, json=login_data, timeout=10)
        print(f"Login response: {response.status_code}")
        
        if response.status_code != 200:
            print(f"Login failed: {response.text}")
            return False
        
        token = response.json().get("token")
        print("✅ Login successful")
        
        # Step 2: Check current documents
        print("\n2. Checking current documents...")
        list_url = "http://localhost:5000/api/documents/list"
        headers = {"Authorization": f"Bearer {token}"}
        
        response = requests.get(list_url, headers=headers, timeout=10)
        print(f"List response: {response.status_code}")
        
        if response.status_code == 200:
            documents = response.json().get("documents", [])
            print(f"Current documents: {len(documents)}")
            for doc in documents:
                print(f"  - {doc}")
        else:
            print(f"List failed: {response.text}")
        
        # Step 3: Upload document
        print("\n3. Uploading document...")
        file_path = os.path.join("uploads", "gabriel", "Backend_Four_Hour_Test_1.pdf")
        
        if not os.path.exists(file_path):
            print(f"❌ File not found: {file_path}")
            return False
        
        upload_url = "http://localhost:5000/api/documents/upload"
        
        with open(file_path, 'rb') as f:
            files = {'file': ('Backend_Four_Hour_Test_1.pdf', f, 'application/pdf')}
            print(f"Uploading file: {file_path} ({os.path.getsize(file_path)} bytes)")
            
            response = requests.post(upload_url, files=files, headers=headers, timeout=120)
            print(f"Upload response: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Upload successful: {result}")
            else:
                print(f"❌ Upload failed: {response.text}")
                return False
        
        # Step 4: Wait a moment for processing
        print("\n4. Waiting for processing...")
        time.sleep(5)
        
        # Step 5: Check documents again
        print("\n5. Checking documents after upload...")
        response = requests.get(list_url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            documents = response.json().get("documents", [])
            print(f"Documents after upload: {len(documents)}")
            for doc in documents:
                print(f"  - {doc}")
            
            if len(documents) > 0:
                print("✅ Document successfully processed and stored!")
                return True
            else:
                print("❌ Document uploaded but not found in list")
                return False
        else:
            print(f"❌ List failed after upload: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main function."""
    print("Simple document upload test\n")
    
    success = test_upload()
    
    if success:
        print("\n🎉 Test completed successfully!")
    else:
        print("\n💥 Test failed!")

if __name__ == "__main__":
    main()
