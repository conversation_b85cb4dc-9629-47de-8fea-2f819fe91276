"""
<PERSON><PERSON>t to migrate existing Qdrant data to the new Docker service.
"""
import os
import sys
import requests
import time

def wait_for_qdrant(url="http://localhost:6333", max_retries=30):
    """Wait for Qdrant service to be ready."""
    print(f"Waiting for Qdrant service at {url}...")

    for attempt in range(max_retries):
        try:
            response = requests.get(f"{url}/", timeout=5)
            if response.status_code == 200:
                print("✅ Qdrant service is ready!")
                return True
        except requests.exceptions.RequestException:
            pass

        print(f"Attempt {attempt + 1}/{max_retries} - waiting...")
        time.sleep(2)

    print("❌ Qdrant service not ready after waiting")
    return False

def migrate_data():
    """Migrate data from local Qdrant to Docker service."""
    print("Starting Qdrant data migration...")

    # Check if local data exists
    local_qdrant_path = "./backend/qdrant_data"
    if not os.path.exists(local_qdrant_path):
        print("No local Qdrant data found to migrate")
        return True

    print(f"Found local Qdrant data at: {local_qdrant_path}")

    try:
        # Import here to avoid issues if not available
        sys.path.append('./backend')
        from src.services.vector_db_service import VectorDBService

        # Create vector service (this will connect to the new Docker service)
        print("Connecting to new Qdrant service...")
        vector_service = VectorDBService()

        if not vector_service.client:
            print("❌ Could not connect to new Qdrant service")
            return False

        print("✅ Connected to new Qdrant service")

        # Check if collection exists and has data
        try:
            collection_info = vector_service.client.get_collection("health_documents")
            print(f"Collection info: {collection_info}")

            if collection_info.points_count > 0:
                print(f"✅ Collection already has {collection_info.points_count} points")
                print("Migration appears to be complete or data already exists")
                return True
            else:
                print("Collection exists but is empty - this is expected for a new service")

        except Exception as e:
            print(f"Collection doesn't exist yet: {e}")

        print("✅ Migration setup complete")
        print("Note: You can now upload documents through the UI and they will be stored in the new Qdrant service")

        return True

    except Exception as e:
        print(f"❌ Error during migration: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main migration function."""
    print("Qdrant Data Migration Tool\n")

    # Wait for Qdrant service
    if not wait_for_qdrant():
        print("❌ Migration failed - Qdrant service not available")
        return

    # Migrate data
    if migrate_data():
        print("\n🎉 Migration completed successfully!")
        print("\nNext steps:")
        print("1. The new Qdrant service is running on port 6333")
        print("2. Upload documents through the UI to test the new setup")
        print("3. Documents will be stored in the Docker volume 'qdrant-data-dev'")
    else:
        print("\n💥 Migration failed!")

if __name__ == "__main__":
    main()
