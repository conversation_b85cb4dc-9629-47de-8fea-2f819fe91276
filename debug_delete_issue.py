import requests
import json

def debug_delete_issue():
    """Debug the document delete issue."""
    print("🔍 Debugging document delete issue...")
    
    # Login
    login_data = {'username': 'gabriel', 'password': 'gabriel123'}
    response = requests.post('http://localhost:5000/api/auth/login', json=login_data, timeout=10)
    
    if response.status_code != 200:
        print('❌ Login failed')
        return
    
    token = response.json().get('token')
    headers = {'Authorization': f'Bearer {token}'}
    print('✅ Login successful')
    
    # Get current documents
    print('\n📋 Getting current documents...')
    response = requests.get('http://localhost:5000/api/documents/list', headers=headers, timeout=10)
    
    if response.status_code != 200:
        print(f'❌ Failed to get documents: {response.status_code}')
        return
    
    documents = response.json().get('documents', [])
    print(f'✅ Found {len(documents)} documents:')
    
    for doc in documents:
        filename = doc.get('filename', 'Unknown')
        doc_id = doc.get('id', 'Unknown')
        metadata = doc.get('metadata', {})
        print(f'  📄 {filename}')
        print(f'    ID: {doc_id}')
        print(f'    Metadata: {json.dumps(metadata, indent=6)}')
    
    if not documents:
        print('ℹ️  No documents to test delete with')
        return
    
    # Test delete with the first document
    test_doc = documents[0]
    test_filename = test_doc.get('filename')
    
    print(f'\n🗑️  Testing delete for: {test_filename}')
    
    # Try to delete
    delete_url = f'http://localhost:5000/api/documents/delete/{test_filename}'
    print(f'Delete URL: {delete_url}')
    
    response = requests.delete(delete_url, headers=headers, timeout=10)
    
    print(f'Delete response status: {response.status_code}')
    print(f'Delete response headers: {dict(response.headers)}')
    
    try:
        response_data = response.json()
        print(f'Delete response data: {json.dumps(response_data, indent=2)}')
    except:
        print(f'Delete response text: {response.text}')
    
    # Check if document still exists in list
    print('\n📋 Checking documents after delete attempt...')
    response = requests.get('http://localhost:5000/api/documents/list', headers=headers, timeout=10)
    
    if response.status_code == 200:
        new_documents = response.json().get('documents', [])
        print(f'Documents after delete: {len(new_documents)}')
        
        # Check if the document was actually removed from the list
        remaining_filenames = [doc.get('filename') for doc in new_documents]
        if test_filename in remaining_filenames:
            print(f'❌ Document {test_filename} still in list (delete may have failed)')
        else:
            print(f'✅ Document {test_filename} removed from list (delete may have succeeded despite 404)')
    
    # Check if file still exists on disk
    print('\n💾 Checking if file exists on disk...')
    try:
        download_response = requests.get(f'http://localhost:5000/api/documents/download/{test_filename}', 
                                       headers=headers, timeout=10)
        
        if download_response.status_code == 200:
            print(f'❌ File still downloadable (delete failed)')
        elif download_response.status_code == 404:
            print(f'✅ File not downloadable (delete succeeded)')
        else:
            print(f'⚠️  Unexpected download status: {download_response.status_code}')
    except Exception as e:
        print(f'⚠️  Error testing download: {e}')

def main():
    """Main debug function."""
    print("🧪 Document Delete Debug Test\n")
    debug_delete_issue()

if __name__ == "__main__":
    main()
