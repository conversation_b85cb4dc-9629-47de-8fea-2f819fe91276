# PowerShell script to start the AI Health Chatbot with separate Qdrant service

Write-Host "🚀 Starting AI Health Chatbot with separate Qdrant service..." -ForegroundColor Green

# Stop any existing containers
Write-Host "Stopping existing containers..." -ForegroundColor Yellow
docker-compose -f docker-compose.dev.yml down

# Start the new stack
Write-Host "Starting new stack with Qdrant service..." -ForegroundColor Yellow
docker-compose -f docker-compose.dev.yml up -d

# Wait a moment for services to start
Write-Host "Waiting for services to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Check service status
Write-Host "Checking service status..." -ForegroundColor Yellow
docker-compose -f docker-compose.dev.yml ps

# Test Qdrant service
Write-Host "Testing Qdrant service..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "http://localhost:6333/health" -TimeoutSec 5
    Write-Host "✅ Qdrant service is healthy: $($response)" -ForegroundColor Green
} catch {
    Write-Host "❌ Qdrant service not responding yet" -ForegroundColor Red
}

# Test backend service
Write-Host "Testing backend service..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "http://localhost:5000/api/health" -TimeoutSec 10
    Write-Host "✅ Backend service is healthy: $($response.status)" -ForegroundColor Green
} catch {
    Write-Host "❌ Backend service not responding yet" -ForegroundColor Red
}

Write-Host "`n🎉 Setup complete!" -ForegroundColor Green
Write-Host "Services:" -ForegroundColor Cyan
Write-Host "  - Qdrant: http://localhost:6333" -ForegroundColor White
Write-Host "  - Backend: http://localhost:5000" -ForegroundColor White
Write-Host "  - Frontend: http://localhost:3000" -ForegroundColor White
Write-Host "`nNext steps:" -ForegroundColor Cyan
Write-Host "  1. Run migration script: python migrate_qdrant_data.py" -ForegroundColor White
Write-Host "  2. Open frontend at http://localhost:3000" -ForegroundColor White
Write-Host "  3. Test document upload functionality" -ForegroundColor White
