"""
Test script to check if the embedding service is working.
"""
import sys
import os

def test_embedding_service():
    """Test the embedding service in isolation."""
    print("Testing embedding service...")
    
    try:
        from src.services.medical_embedding_service import MedicalEmbeddingService
        
        # Create embedding service
        embedding_service = MedicalEmbeddingService()
        
        # Test text embedding
        test_text = "This is a test document about diabetes."
        print(f"Testing embedding for: '{test_text}'")
        
        embedding = embedding_service.get_text_embedding(test_text)
        print(f"✅ Embedding generated successfully")
        print(f"Embedding shape: {embedding.shape}")
        print(f"Embedding type: {type(embedding)}")
        print(f"First 5 values: {embedding[:5]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing embedding service: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_document_processor():
    """Test the document processor in isolation."""
    print("\nTesting document processor...")
    
    try:
        from src.utils.document_processor import DocumentProcessor
        
        # Create document processor
        processor = DocumentProcessor()
        
        # Test with <PERSON>'s PDF
        file_path = os.path.join("uploads", "gabriel", "Backend_Four_Hour_Test_1.pdf")
        
        if not os.path.exists(file_path):
            print(f"❌ File not found: {file_path}")
            return False
        
        print(f"Processing file: {file_path}")
        chunks = processor.process_file(file_path)
        
        print(f"✅ Document processed successfully")
        print(f"Number of chunks: {len(chunks)}")
        
        if chunks:
            print(f"First chunk content preview: {chunks[0]['content'][:100]}...")
            print(f"First chunk metadata: {chunks[0]['metadata']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing document processor: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_embedding_chunks():
    """Test embedding document chunks."""
    print("\nTesting embedding of document chunks...")
    
    try:
        from src.services.medical_embedding_service import MedicalEmbeddingService
        from src.utils.document_processor import DocumentProcessor
        
        # Create services
        embedding_service = MedicalEmbeddingService()
        processor = DocumentProcessor()
        
        # Process Gabriel's document
        file_path = os.path.join("uploads", "gabriel", "Backend_Four_Hour_Test_1.pdf")
        
        if not os.path.exists(file_path):
            print(f"❌ File not found: {file_path}")
            return False
        
        print("Processing document...")
        chunks = processor.process_file(file_path)
        
        if not chunks:
            print("❌ No chunks generated")
            return False
        
        print(f"Generated {len(chunks)} chunks")
        
        # Add metadata
        for chunk in chunks:
            chunk["metadata"]["user_role"] = "patient"
            chunk["metadata"]["user_id"] = "gabriel"
        
        print("Generating embeddings for chunks...")
        chunks_with_embeddings = embedding_service.embed_document_chunks(chunks)
        
        print(f"✅ Embeddings generated successfully")
        print(f"Chunks with embeddings: {len(chunks_with_embeddings)}")
        
        # Check first chunk
        if chunks_with_embeddings:
            first_chunk = chunks_with_embeddings[0]
            if "embedding" in first_chunk:
                embedding = first_chunk["embedding"]
                print(f"First embedding shape: {embedding.shape}")
                print(f"First embedding type: {type(embedding)}")
            else:
                print("❌ No embedding in first chunk")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing embedding chunks: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    print("Testing individual components...\n")
    
    success1 = test_embedding_service()
    success2 = test_document_processor()
    success3 = test_embedding_chunks()
    
    if success1 and success2 and success3:
        print("\n🎉 All components working correctly!")
        print("The issue is likely with the VectorDBService (Qdrant) storage.")
    else:
        print("\n💥 Some components have issues!")

if __name__ == "__main__":
    main()
