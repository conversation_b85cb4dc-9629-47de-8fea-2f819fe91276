"""
Debug script to understand why documents are not appearing in the list.
"""
import os
import sys
from src.services.document_service import DocumentService
from src.services.vector_db_service import VectorDBService

def debug_vector_db():
    """Debug the vector database directly."""
    print("Debugging vector database...")
    
    try:
        # Create a new vector DB service instance
        vector_service = VectorDBService()
        
        print(f"Client initialized: {vector_service.client is not None}")
        print(f"Collection name: {vector_service.collection_name}")
        
        if vector_service.client:
            # Try to get collection info
            try:
                collections = vector_service.client.get_collections()
                print(f"Available collections: {[c.name for c in collections.collections]}")
                
                # Check if our collection exists
                if vector_service.collection_name in [c.name for c in collections.collections]:
                    print(f"✅ Collection '{vector_service.collection_name}' exists")
                    
                    # Get collection info
                    collection_info = vector_service.client.get_collection(vector_service.collection_name)
                    print(f"Collection info: {collection_info}")
                    
                    # Try to scroll through all points
                    print("\nScrolling through all points...")
                    scroll_result = vector_service.client.scroll(
                        collection_name=vector_service.collection_name,
                        limit=10,
                        with_payload=True,
                        with_vectors=False
                    )
                    
                    points = scroll_result[0]
                    print(f"Found {len(points)} points")
                    
                    for i, point in enumerate(points):
                        print(f"Point {i+1}:")
                        print(f"  ID: {point.id}")
                        print(f"  Payload: {point.payload}")
                        
                        if "metadata" in point.payload:
                            metadata = point.payload["metadata"]
                            print(f"  Source: {metadata.get('source', 'N/A')}")
                            print(f"  User ID: {metadata.get('user_id', 'N/A')}")
                            print(f"  User Role: {metadata.get('user_role', 'N/A')}")
                        print()
                    
                    # Try to search for gabriel's documents specifically
                    print("Searching for gabriel's documents...")
                    gabriel_scroll = vector_service.client.scroll(
                        collection_name=vector_service.collection_name,
                        limit=10,
                        with_payload=True,
                        with_vectors=False,
                        filter={
                            "must": [
                                {
                                    "key": "metadata.user_id",
                                    "match": {"value": "gabriel"}
                                }
                            ]
                        }
                    )
                    
                    gabriel_points = gabriel_scroll[0]
                    print(f"Found {len(gabriel_points)} points for gabriel")
                    
                    for i, point in enumerate(gabriel_points):
                        print(f"Gabriel point {i+1}:")
                        print(f"  ID: {point.id}")
                        metadata = point.payload.get("metadata", {})
                        print(f"  Source: {metadata.get('source', 'N/A')}")
                        print(f"  User ID: {metadata.get('user_id', 'N/A')}")
                        print()
                        
                else:
                    print(f"❌ Collection '{vector_service.collection_name}' does not exist")
                    
            except Exception as e:
                print(f"Error accessing collection: {e}")
                import traceback
                traceback.print_exc()
        else:
            print("❌ Vector database client not initialized")
            
    except Exception as e:
        print(f"Error debugging vector database: {e}")
        import traceback
        traceback.print_exc()

def debug_document_service():
    """Debug the document service."""
    print("\nDebugging document service...")
    
    try:
        doc_service = DocumentService()
        
        # Test get_documents_by_user
        print("Testing get_documents_by_user for gabriel...")
        documents = doc_service.get_documents_by_user("gabriel")
        print(f"Found {len(documents)} documents for gabriel:")
        for doc in documents:
            print(f"  - {doc}")
            
        # Test list_documents (all documents)
        print("\nTesting list_documents (all)...")
        all_sources = doc_service.list_documents()
        print(f"All sources: {all_sources}")
        
    except Exception as e:
        print(f"Error debugging document service: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main function."""
    print("Debugging document issue...\n")
    
    debug_vector_db()
    debug_document_service()
    
    print("\nDebug completed.")

if __name__ == "__main__":
    main()
