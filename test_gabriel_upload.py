"""
Test uploading <PERSON>'s document to the new setup.
"""
import requests
import os

def test_gabriel_upload():
    """Test uploading <PERSON>'s document."""
    print("Testing <PERSON>'s document upload...")
    
    # Step 1: Login
    print("1. Logging in as <PERSON>...")
    login_data = {"username": "gab<PERSON>", "password": "gabriel123"}
    response = requests.post("http://localhost:5000/api/auth/login", json=login_data, timeout=10)
    
    if response.status_code != 200:
        print(f"❌ Login failed: {response.text}")
        return False
    
    token = response.json().get("token")
    headers = {"Authorization": f"Bearer {token}"}
    print("✅ Login successful")
    
    # Step 2: Check current documents
    print("\n2. Checking current documents...")
    response = requests.get("http://localhost:5000/api/documents/list", headers=headers, timeout=10)
    
    if response.status_code == 200:
        documents = response.json().get("documents", [])
        print(f"Current documents: {len(documents)}")
        for doc in documents:
            print(f"  - {doc.get('filename', 'Unknown')}")
    else:
        print(f"❌ Failed to list documents: {response.text}")
        return False
    
    # Step 3: Upload <PERSON>'s PDF
    print("\n3. Uploading Gabriel's PDF...")
    file_path = "backend/uploads/gabriel/Backend_Four_Hour_Test_1.pdf"
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return False
    
    print(f"File found: {file_path} ({os.path.getsize(file_path)} bytes)")
    
    try:
        with open(file_path, 'rb') as f:
            files = {'file': ('Backend_Four_Hour_Test_1.pdf', f, 'application/pdf')}
            response = requests.post("http://localhost:5000/api/documents/upload", 
                                   files=files, headers=headers, timeout=120)
        
        print(f"Upload response: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Upload successful: {result}")
        else:
            print(f"❌ Upload failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Upload error: {e}")
        return False
    
    # Step 4: Wait and check documents again
    print("\n4. Waiting for processing...")
    import time
    time.sleep(5)
    
    print("5. Checking documents after upload...")
    response = requests.get("http://localhost:5000/api/documents/list", headers=headers, timeout=10)
    
    if response.status_code == 200:
        documents = response.json().get("documents", [])
        print(f"Documents after upload: {len(documents)}")
        for doc in documents:
            print(f"  - {doc.get('filename', 'Unknown')}")
        
        # Check if Gabriel's document is there
        gabriel_doc = any(doc.get('filename') == 'Backend_Four_Hour_Test_1.pdf' for doc in documents)
        if gabriel_doc:
            print("✅ Gabriel's document found in list!")
            return True
        else:
            print("⚠️  Gabriel's document not found in list")
            return False
    else:
        print(f"❌ Failed to list documents after upload: {response.text}")
        return False

def main():
    """Main function."""
    print("Gabriel Document Upload Test\n")
    
    if test_gabriel_upload():
        print("\n🎉 Gabriel's document upload test successful!")
        print("The new Qdrant setup is working correctly!")
    else:
        print("\n💥 Gabriel's document upload test failed!")

if __name__ == "__main__":
    main()
