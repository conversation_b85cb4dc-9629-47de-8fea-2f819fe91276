"""
<PERSON><PERSON><PERSON> to manually process <PERSON>'s document and store it in Qdrant.
"""
import os
import sys
from src.services.document_service import DocumentService

def process_gabriel_document():
    """Process Gabriel's document manually."""
    print("Processing <PERSON>'s document...")
    
    # Document details
    user_id = "gabriel"
    user_role = "patient"
    filename = "Backend_Four_Hour_Test_1.pdf"
    file_path = os.path.join("uploads", user_id, filename)
    
    # Check if file exists
    if not os.path.exists(file_path):
        print(f"Error: File {file_path} does not exist")
        return False
    
    print(f"File found: {file_path}")
    file_size = os.path.getsize(file_path)
    print(f"File size: {file_size} bytes")
    
    try:
        # Create document service
        document_service = DocumentService()
        
        # Process and store the document
        print("Processing document...")
        document_id = document_service.process_and_store_document(
            file_path,
            filename,
            user_role=user_role,
            user_id=user_id
        )
        
        print(f"Successfully processed document with ID: {document_id}")
        
        # Verify the document was stored
        print("Verifying document was stored...")
        documents = document_service.get_documents_by_user(user_id)
        print(f"Found {len(documents)} documents for {user_id}:")
        for doc in documents:
            print(f"  - {doc}")
        
        return True
        
    except Exception as e:
        print(f"Error processing document: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    print("Manual document processing script\n")
    
    success = process_gabriel_document()
    
    if success:
        print("\n✅ Document processing completed successfully!")
    else:
        print("\n❌ Document processing failed!")

if __name__ == "__main__":
    main()
