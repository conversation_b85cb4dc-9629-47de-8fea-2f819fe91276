"""
Monitor API calls while testing frontend.
"""
import requests
import time
import threading

def test_api_periodically():
    """Test API calls periodically to monitor performance."""
    print("Starting API monitoring...")
    
    login_data = {"username": "gab<PERSON>", "password": "gabriel123"}
    
    while True:
        try:
            print(f"\n[{time.strftime('%H:%M:%S')}] Testing API...")
            
            # Login
            start_time = time.time()
            response = requests.post("http://localhost:5000/api/auth/login", 
                                   json=login_data, timeout=5)
            login_time = time.time() - start_time
            
            if response.status_code == 200:
                print(f"  ✅ Login: {login_time:.2f}s")
                token = response.json().get("token")
                headers = {"Authorization": f"Bearer {token}"}
                
                # Test document list
                start_time = time.time()
                try:
                    response = requests.get("http://localhost:5000/api/documents/list", 
                                          headers=headers, timeout=10)
                    list_time = time.time() - start_time
                    
                    if response.status_code == 200:
                        documents = response.json().get("documents", [])
                        print(f"  ✅ Document list: {list_time:.2f}s ({len(documents)} docs)")
                        for doc in documents:
                            print(f"    - {doc.get('filename', 'Unknown')}")
                    else:
                        print(f"  ❌ Document list failed: {response.status_code}")
                except requests.exceptions.Timeout:
                    print(f"  ⏰ Document list timeout after 10s")
                except Exception as e:
                    print(f"  ❌ Document list error: {e}")
            else:
                print(f"  ❌ Login failed: {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ API test error: {e}")
        
        time.sleep(30)  # Test every 30 seconds

if __name__ == "__main__":
    test_api_periodically()
