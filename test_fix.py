import requests
import time

print('Testing API after fix at', time.strftime('%H:%M:%S'))
login_data = {'username': 'gab<PERSON>', 'password': 'gabriel123'}
response = requests.post('http://localhost:5000/api/auth/login', json=login_data, timeout=5)
print('Login status:', response.status_code)

if response.status_code == 200:
    token = response.json().get('token')
    headers = {'Authorization': f'Bearer {token}'}
    print('Testing document list...')
    start_time = time.time()
    
    try:
        response = requests.get('http://localhost:5000/api/documents/list', headers=headers, timeout=15)
        elapsed = time.time() - start_time
        print(f'Document list status: {response.status_code} (took {elapsed:.2f}s)')
        
        if response.status_code == 200:
            documents = response.json().get('documents', [])
            print(f'Found {len(documents)} documents:')
            for doc in documents:
                print(f'  - {doc.get("filename", "Unknown")}')
        else:
            print('Response:', response.text)
    except Exception as e:
        elapsed = time.time() - start_time
        print(f'Document list error after {elapsed:.2f}s:', str(e))
