import requests

login_data = {'username': 'gab<PERSON>', 'password': 'gabriel123'}
response = requests.post('http://localhost:5000/api/auth/login', json=login_data, timeout=5)

if response.status_code == 200:
    token = response.json().get('token')
    headers = {'Authorization': f'Bearer {token}'}
    response = requests.get('http://localhost:5000/api/documents/list', headers=headers, timeout=10)
    
    if response.status_code == 200:
        documents = response.json().get('documents', [])
        print('Documents in API:')
        for doc in documents:
            print(f'  - {doc.get("filename", "Unknown")}')
    else:
        print('API Error:', response.text)
else:
    print('Login failed')
