version: '3.8'

services:
  qdrant:
    image: qdrant/qdrant:latest
    container_name: health-chatbot-qdrant-dev
    restart: unless-stopped
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant-data-dev:/qdrant/storage
    networks:
      - health-chatbot-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: health-chatbot-backend-dev
    restart: unless-stopped
    environment:
      - FLASK_ENV=development
      - PORT=5000
      - SECRET_KEY=${SECRET_KEY:-dev-secret-key}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-dev-jwt-key}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - DEV_JWT_KEY=${DEV_JWT_KEY:-dev-jwt-key}
      - DEV_OPENAI_KEY=${DEV_OPENAI_KEY}
      - VECTOR_DB_URL=http://qdrant:6333
      - VECTOR_DB_COLLECTION=health_documents
    ports:
      - "5000:5000"
    volumes:
      - ./backend:/app
      - /app/venv
      - /app/__pycache__
    depends_on:
      qdrant:
        condition: service_healthy
    networks:
      - health-chatbot-network
    command: ["python", "app.py"]

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: health-chatbot-frontend-dev
    restart: unless-stopped
    environment:
      - REACT_APP_API_URL=http://localhost:5000
      - CHOKIDAR_USEPOLLING=true
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - health-chatbot-network
    command: ["npm", "start"]

networks:
  health-chatbot-network:
    driver: bridge

volumes:
  qdrant-data-dev:
    driver: local
