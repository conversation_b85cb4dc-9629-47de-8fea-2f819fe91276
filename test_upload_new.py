import requests
import tempfile
import os
import time

print('Testing new document upload...')

# Create a test document
test_content = """
Test Document for <PERSON>

This is a test document about diabetes management and health monitoring.

Key points:
- Regular blood sugar monitoring
- Proper medication adherence
- Healthy diet and exercise
- Regular doctor visits

This document should appear in the Document Management interface immediately after upload.
"""

# Login
login_data = {'username': 'gabriel', 'password': 'gabriel123'}
response = requests.post('http://localhost:5000/api/auth/login', json=login_data, timeout=5)

if response.status_code == 200:
    token = response.json().get('token')
    headers = {'Authorization': f'Bearer {token}'}
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        f.write(test_content)
        temp_file_path = f.name
    
    try:
        # Upload the document
        print('Uploading test document...')
        with open(temp_file_path, 'rb') as f:
            files = {'file': ('test_diabetes_guide.txt', f, 'text/plain')}
            response = requests.post('http://localhost:5000/api/documents/upload', 
                                   files=files, headers=headers, timeout=60)
        
        print(f'Upload status: {response.status_code}')
        if response.status_code == 200:
            result = response.json()
            print(f'Upload result: {result}')
            
            # Wait a moment for processing
            time.sleep(2)
            
            # Check document list
            print('Checking document list...')
            response = requests.get('http://localhost:5000/api/documents/list', headers=headers, timeout=10)
            
            if response.status_code == 200:
                documents = response.json().get('documents', [])
                print(f'Total documents: {len(documents)}')
                for doc in documents:
                    print(f'  - {doc.get("filename", "Unknown")}')
            else:
                print('Error listing documents:', response.text)
        else:
            print('Upload failed:', response.text)
            
    finally:
        os.unlink(temp_file_path)
else:
    print('Login failed')
